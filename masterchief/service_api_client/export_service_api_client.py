# directly posrted copy/paste from client-server
# (client_server_env): $ python client/retail/api/service/export_service_api_client.py
# requirements.txt
#   requests==2.32.3

import os
import datetime
import logging
import json

from pprint import pprint

import requests
# import requests.status_codes
from requests.adapters import HTTPAdapter

try:
    from common.utils import parse_dt, get_utc_now
except ImportError:
    from service_api_client.es_utils import parse_dt, get_utc_now

logger = logging.getLogger('api_client')

CLIENT_SERVICE_API_TOKEN = os.environ.get('CLIENT_SERVICE_API_TOKEN', 'KyavtdLqeNNU0bPzQcCgYpLudPJM25Cg')
# atm ENV client-server hosts possible: https://api.modatech.ru, https://api.garderobo.ai
CLIENT_SERVICE_API_HOST = os.environ.get('CLIENT_SERVICE_API_HOST', 'http://127.0.0.1:8001')
CLIENT_SERVICE_API_ENDPOINT = CLIENT_SERVICE_API_HOST + '/api/v3/export/'
SESSION_TIMEOUT = 120

DEFAULT_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S.%f'


class MyHTTPAdapter(HTTPAdapter):
    def __init__(self, timeout=SESSION_TIMEOUT, *args, **kwargs):
        self.timeout = timeout
        super(MyHTTPAdapter, self).__init__(*args, **kwargs)

    def send(self, *args, **kwargs):
        kwargs['timeout'] = self.timeout
        return super().send(*args, **kwargs)


class ServiceApiClient:
    endpoint = CLIENT_SERVICE_API_ENDPOINT

    @property
    def headers(self):
        return {
            'X-API-TOKEN': CLIENT_SERVICE_API_TOKEN
        }

    @property
    def requests_session(self):
        """
        max retries is just 1 for recommendator session,
        to prevent case with a lot of retries in sync mode and widget user waiting for response
        """
        session = requests.Session()
        adapter = MyHTTPAdapter(max_retries=1, timeout=SESSION_TIMEOUT)
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        return session

    @property
    def valid_response_statuses(self):
        return [200]

    def handle_errored_response(self, response, get_raw_response=False):
        print(
            response.status_code
        )
        print(
            response.text
        )
        print(f'Request is no allowed, check api tokens are filled in headers are valid: {self.headers}')

    def get_vendors(self):
        """
        [{'code': '5karmanov', 'id': 38, 'settings': {}},...]
        """
        response = self.requests_session.get(
            self.endpoint + 'vendors/',
            headers=self.headers
        )
        if response.status_code not in self.valid_response_statuses:
            return self.handle_errored_response(response)

        data = response.json()
        return data['vendors']

    def get_retail_categories(self):
        """
        [
            {'category_group':
                {'code': 'accessory', 'id': 98, 'name': 'Аксессуары'},
            'full_name': 'Аксессуары',
            'id': 59,
            'is_cloth': False,
            'root_id': None},

            {'category_group':
                {'code': 'accessory_tie', 'id': 97, 'name': 'Галстуки, бабочки'},
            'full_name': 'Аксессуары > Галстуки, бабочки',
            'id': 315,
            'is_cloth': False,
            'root_id': 59},
        ...]
        """
        response = self.requests_session.get(
            self.endpoint + 'categories/',
            headers=self.headers
        )
        if response.status_code not in self.valid_response_statuses:
            return self.handle_errored_response(response)

        data = response.json()
        pprint(
            data['categories']
        )

    def chunked_datas_iterator(self, data, method, data_key='products', on_last_chunk=None, **kwargs):
        while True:
            yield data[data_key]
            next_page = data.get('next_page')
            if next_page:
                # page_size=data['page_size']; kwargs['page_size'] = data['page_size']
                data = method(page=next_page, **kwargs)
            else:
                if on_last_chunk:
                    if callable(on_last_chunk):
                        on_last_chunk = on_last_chunk()
                    self.send_on_last_chunk(on_last_chunk=on_last_chunk)
                break

    def _debug_request(self, **kwargs):
        pprint(
            kwargs
        )
        print(
            self.endpoint + 'products/'
        )

    def get_chunked_datas(self, method, data_key='products', on_last_chunk=None, **kwargs):
        # assert method
        data = method(**kwargs)
        if not data:
            self._debug_request(method=method, data_key=data_key, **kwargs)
            raise Exception('Endpoint response is not valid json')

        return {
            #'page_size': data['page_size'],
            'count': data['total_products_count'],
            'total_pages': data['num_pages'],
            'chunks': enumerate(
                self.chunked_datas_iterator(data, method, data_key=data_key, on_last_chunk=on_last_chunk, **kwargs), 1)
        }

    def get_products_chunked_datas(self, **kwargs):
        return self.get_chunked_datas(
            self.get_products, data_key='products', **kwargs
        )

    def send_on_last_chunk(self, on_last_chunk=None, get_raw_response=False):
        """
        when we recieve all chunks of products, we send service_name/dt_latest,
        and masterchief stores them in named Settings fpor service_name
        next time we are fetching products starting from dt_latest for special named service_name
        """
        on_last_chunk = on_last_chunk or {}

        response = self.requests_session.post(
            self.endpoint + 'on_last_chunk/',
            headers=self.headers,
            json=on_last_chunk
        )
        if response.status_code not in self.valid_response_statuses:
            logger.error(f'masterchief on_last_chunk api error, response: {response.text}')
            return self.handle_errored_response(response, get_raw_response=get_raw_response)

    def get_dt_latest(self, **payload):
        try:
            response = self.requests_session.post(
                self.endpoint + 'service_settings/',
                headers=self.headers,
                json=payload
            )
            return response.json().get('dt_latest', None)
        except Exception as e:
            logger.error(f'masterchief service settings api error, payload: {payload}')
            logger.exception(e)
            raise Exception(e)

    def get_products(self, filters=None, features=None, page=None, page_size=100,
                     payload_addon=None, get_raw_response=False, get_all_pages=False):
        """
        'indexes': [1, 5],
         'next_page': 2,
         'next_page_url': '/api/v3/export/products/?page=2&page_size=5',
         'products': [{'brand_id': 1419,
                       'brand_name': 'Falke',
                       'gender': 'Девочки',
                       'group': 'socks',
                       'id': 1007346,
                       'img': 'https://media.garderobo.ru/kenguru/big/9b31f4b00d78fecc7a7696e33dc1cbb9.jpg',
                       'name': 'Белые гольфы с геометрическим принтом',
                       'rc': 'Домашняя одежда и бельё > Носки',
                       'rc_id': 162,
                       'season': None,
                       'vendor_color': 'Белый'},

        """
        payload = {'page_size': page_size}
        if page:
            payload['page'] = page

        if filters:
            try:
                json.dumps(filters)
            except Exception as e:
                logger.error('products endpoint filters dict values should be JSON serializable')
                logger.exception(e)
                raise e

            payload['filters'] = filters
        if features:
            # by default, we use dict from client/retail/api/service/viewsets.py:403
            payload['features'] = features

        if payload_addon:
            payload.update(payload_addon)

        response = self.requests_session.post(
            self.endpoint + 'products/',
            headers=self.headers,
            json=payload
        )
        if response.status_code not in self.valid_response_statuses:
            return self.handle_errored_response(response, get_raw_response=get_raw_response)

        data = response.json()
        if get_raw_response:
            return data

        products_collected = data.get('products') or []

        if get_all_pages:
            next_page_endpoint = data.get('next_page_url')
            while next_page_endpoint:
                endpoint_url = self.endpoint + 'products/' + '?' + next_page_endpoint.split('?', 1)[-1]
                response = self.requests_session.post(
                    endpoint_url,
                    headers=self.headers,
                    json=payload
                )
                data = response.json()
                products_collected.extend(data.get('products') or [])
                next_page_endpoint = data.get('next_page_url')

        return products_collected

    @classmethod
    def test_it_all(self):
        client = ServiceApiClient()
        # client.get_vendors()
        # client.get_retail_categories()
        dt_from = datetime.datetime(2025, 4, 1, 0, 0, 0, 0, tzinfo=datetime.timezone.utc)
        client.get_products(
            filters={
                # 'vendor_id': 41,
                'vendors_ids': [193,],
                'dt_from': str(dt_from),
            },
            # if no specified, will be used defaults from api view .features_default
            features={
                'with_vc': 1,
                'with_material': 1,
                'with_tags': 1,
                'with_pictures': 1,
                'active_only': 0
            },
            page_size=2,
            get_all_pages=False,  # True
        )

    @classmethod
    def format_dt_str(cls, dt, fmt=DEFAULT_DATETIME_FORMAT):
        if isinstance(dt, (datetime.date, datetime.datetime)):
            return dt.strftime(fmt)
        # not ad valid datetime
        return dt

    @classmethod
    def parse_dt_str(cls, dt_str, formats=['%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d %H:%M:%S']):
        """
        yet just one supported fmt:
        'dt_from': '2025-04-01 00:00:00.000001+00:00'
        or
        'dt_from': '2025-04-01 00:00:00.000001Z'
        """
        if not dt_str:
            return None
        if '+' in dt_str:
            # assume UTC timezone always
            dt_str = dt_str.rsplit('+', 1)[0]
        elif 'Z' in dt_str:
            # assume UTC timezone always
            dt_str = dt_str.rsplit('Z', 1)[0]
        return parse_dt(dt_str, fmts=formats, as_utc=True)

    @classmethod
    def fill_dt_created_updated(cls, data, dt_default=None, store_str_values=False):
        # fill dt_created/updated from str to datetime
        if dt_default == -1:
            dt_default = get_utc_now()
        for k in 'dt_created', 'dt_updated':
            # on demand e can store str values backup cause taht keys removed in .save from dict json
            if store_str_values and k in data:
                data[f'{k}_str'] = data[k]

            data.setdefault(k, dt_default)
            if isinstance(data[k], str):
                data[k] = cls.parse_dt_str(data[k])
