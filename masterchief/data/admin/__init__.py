from django.contrib import admin, messages

from data.models import Setting<PERSON>, Vendor, Product


class BaseModelAdminMixin:
    def get_form(self, *args, **kwargs):
        form = super().get_form(*args, **kwargs)
        return form
        # to_end = []
        # ordered_fields = OrderedDict()
        # for f, field in form.base_fields.items():
        #     if f in ['backend_id', 'is_deleted', ]:
        #         to_end.append((f, field))
        #     else:
        #         ordered_fields[f] = field
        # for f, field in to_end:
        #     ordered_fields[f] = field
        # form.base_fields = ordered_fields
        # return form


class BaseModelAdmin(BaseModelAdminMixin, admin.ModelAdmin):
    def _prepare_actions_queryset(self, qs):
        return qs._clone().order_by()


class VendorFilter(admin.SimpleListFilter):
    title = 'Вендор'
    parameter_name = 'vendor'
    template = 'admin/dd_filter.html'

    _lookups_cache = None

    def lookups(self, request, model_admin):
        if self._lookups_cache is None:
            self._lookups_cache = ((x.id, f'#{x.id} {x.name} [{x.code}]') for x in Vendor.objects.all())
        return self._lookups_cache

    def queryset(self, request, queryset):
        value = self.value()
        if value:
            return queryset.filter(**{self.parameter_name: value})


@admin.register(Settings)
class SettingsAdmin(BaseModelAdmin):
    list_display = ['name', 'dt_updated', 'settings', 'is_deleted']
    readonly_fields = ['dt_created', 'dt_updated']

    def save_model(self, request, obj, form, change):
        obj.changer = request.user
        super().save_model(request, obj, form, change)
        warnings = getattr(obj, 'warnings_on_save', None)
        if warnings:
            messages.add_message(request, messages.WARNING, '; '.join(warnings))


@admin.register(Vendor)
class VendorAdmin(BaseModelAdmin):
    list_display = ['id', 'name', 'settings', 'is_deleted', 'params']
    readonly_fields = ['dt_created', 'dt_updated', 'dt_synced']


@admin.register(Product)
class ProductAdmin(BaseModelAdmin):
    list_display = ['id', 'name', 'vendor', 'is_deleted']
    readonly_fields = ['dt_created', 'dt_updated', 'data']
    search_fields = ['name', 'id']

    list_filter = (
        VendorFilter,  # 'vendor',
    )

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        qs = qs.select_related(
            'vendor'
        )
        return qs

    def get_search_results(self, request, queryset, search_term):
        filtered_custom = may_have_duplicates = False
        search_term = (search_term or '').strip()
        if search_term:
            if search_term.isdigit() and len(search_term)>2:
                queryset = queryset.filter(
                    id=search_term
                )

                filtered_custom = True

        if not filtered_custom:
            queryset, may_have_duplicates = super().get_search_results(
                request,
                queryset,
                search_term,
            )
        return queryset, may_have_duplicates