from collections import defaultdict
from decimal import Decimal
from pprint import pprint

from django.db import transaction

from common.bases import Parser, ParserException
from common import capture_message, logger
from common.utils import cached_property, dict_hash, convert_dd_to_dict, get_now, parse_dt, get_utc_now, \
    raw_delete_queryset, confirm_input
from masterchief.tasks import run_task

from data.models import Vendor, Product  # Category
from service_api_client import ServiceApiClient


class ClientDataImporter(Parser):
    do_refresh_picture_fdh = False
    stats = None

    def __init__(self, data=None, vendor=None, vendor_id=None, *args, **kwargs):
        self.data = data
        self._cache_by_client_id = self._prefill_cache(models=None)
        self.changed_products_ids = defaultdict(list)
        self.vendor = Vendor.get_vendor_by_id_or_code(vendor_id=vendor_id, vendor=vendor)
        self.init_stats()
        self.kwargs = kwargs

    def handle(self, method_name):
        method = getattr(self, method_name, None)
        if not method:
            raise ParserException(f'No method for {method_name}')
        try:
            method()
        except Exception as e:
            logger.exception(e)
            raise ParserException(f'Method {method_name} failed, exc: {e}')
        self.handle_stats(only=[method_name.rsplit('_', 1)[1]])

    def handle_stats(self, only=None):
        stats = self.stats.copy()
        if only:
            stats = {k: v for k, v in self.stats.items() if k in only}
        pprint(
            convert_dd_to_dict(stats)
        )

    @cached_property
    def service_api_client(self):
        return ServiceApiClient()

    def _prefill_cache(self, models=None):
        """
            models=(Vendor, Category,), or
            models=([Product, Product.objects.filter(is_deleted=False)])
        """
        cache = getattr(self, '_cache_by_client_id', None) or defaultdict(lambda: defaultdict())
        if models:
            for model in models:
                if isinstance(model, (tuple, list)):
                    model, qs = model
                else:
                    qs = None
                cache[model] = model.get_objects_by_client_id(qs=qs)

        return cache

    def _get_existing_by_client_id(self, model, qs=None):
        if qs is None:
            qs = model.objects.all()

        self._cache_by_client_id[model] = {o.id: o for o in qs}
        return self._cache_by_client_id[model]

    def handle_categories(self, categories):
        if not categories:
            return
        root_cats_by_childs_client_id = {}
        existing_cats_by_client_id = self._get_existing_by_client_id(Category)

        self.update_stats('categories', 'found_existing', len(existing_cats_by_client_id))
        self.update_stats('categories', 'total_parsed', len(categories))

        for __, c in categories.items():
            # c['id'] is client_id
            c['fn'] = c.get('fn') or c.get('full_name') or ''
            root_cat = root_cats_by_childs_client_id.get(c['id'])
            if c['id'] in existing_cats_by_client_id:
                cat_obj = self._update_category(
                    existing_cats_by_client_id[c['id']], c, root_cat=root_cat)
            else:
                cat_obj = self._create_category(c, root_cat=root_cat)

            for child_client_id in c.get('children', []):
                root_cats_by_childs_client_id[child_client_id] = cat_obj

    def _update_fields(self, existing, data, fields_to_check,
                       log_template=None, commit=True, compare_dicts=True):
        update_fields = []
        # log_template = log_template or '{model} "{obj}" {field} changed: {old} -> {new}'
        dt_fields = {'dt_created', 'dt_updated'}
        dt_fields_to_check = dt_fields.intersection(data)
        if dt_fields_to_check:
            fields_to_check += dt_fields_to_check

        for f in fields_to_check:
            if isinstance(f, tuple):
                f, model = f

                old = getattr(existing, f, None)
                new = self._get_obj_by_client_id(model, data[f])

            else:
                old = getattr(existing, f, None)
                new = data[f]
            if isinstance(new, dict):
                if compare_dicts:
                    hash_old = dict_hash(old)
                    hash_new = dict_hash(new)
                    if hash_old == hash_new:
                        continue
                setattr(existing, f, new)
                update_fields.append(f)
                continue

            if old != new:
                setattr(existing, f, new)
                update_fields.append(f)

        if commit and update_fields:
            existing.save(update_fields=update_fields)
        return update_fields

    def _update_category(self, existing, data, root_cat=None):
        update_fields = self._update_fields(existing, data, ['name', 'fn'])

        is_root_changed = False
        if root_cat:
            existing_root_cat = existing.get_parent()
            if existing_root_cat != root_cat:
                existing.move(root_cat, 'last-child')
                self.logger.debug('Category "{}" has parent changed {} -> {}'.format(
                    existing, existing_root_cat, root_cat
                ))
                is_root_changed = True

        if update_fields or is_root_changed:
            self.update_stats('categories', 'updated')
        else:
            self.update_stats('categories', 'not_changed')

        return existing

    def _create_category(self, data, root_cat=None):
        kwargs = dict(client_id=data['id'], name=data['name'], fn=data['fn'], is_deleted=data['is_deleted'])
        if root_cat:
            created_cat = root_cat.add_child(**kwargs)
        else:
            created_cat = Category.add_root(**kwargs)
        self.update_stats('categories', 'created')
        return created_cat

    def handle_finish(self, finish_data=None):
        pass

    def handle_enabled_vendors_products(self, action=None):
        for vendor in Vendor.get_env_active_vendors_queryset():
            if not vendor.prop_is_enabled_for_sync:
                logger.info(f'Vendor {vendor} is not enabled for sync, skipping...')
                continue

            logger.info(f'Vendor: {vendor}, running task to sync products...')
            run_task(
                'sync', 'products',
                vendor_id=vendor.id,
                action=action,
                run_task_async=False
            )

    def reset_vendor_synced_products(self, confirm=False):
        if confirm and \
            not confirm_input(f'Reset vendor: {self.vendor} synced producs[vendor.products.count()]'):
            return

        result = raw_delete_queryset(self.vendor.products.all())
        self.vendor.prop_dt_synced = None
        logger.info(f'Done reset_vendor_synced_products, result: {result}')

    def import_products(self):
        assert self.vendor
        logger.info(f'Sync product for vendor: {self.vendor}')

        if self.kwargs.get('action') == 'from_scratch':
            self.reset_vendor_synced_products()

        # first we should detect last time vendor products where updated
        dt_synced = self.vendor.prop_dt_synced
        if dt_synced:
            dt_synced = self.service_api_client.format_dt_str(dt_synced)

        page_size = 100
        filters = {
            'vendor_id': self.vendor.id,
            'dt_from': dt_synced,
        }

        # features should correspond to those implemented in client-server
        # check: client/retail/api/service/viewsets.py:403
        features = {
            'active_only': self.vendor.prop_sync_active_only,
            'with_brand': True,
            'with_vendor_colors': True,
            'with_vc': True,
            'with_pictures': True,

            'with_material': True,
            'with_tags': self.vendor.prop_sync_with_tags,
        }
        logger.info(f'Starting sync for vendor: {self.vendor}, dt_from: {dt_synced or "-"}')

        # then we should get products from client since that time
        chunked_data = self.service_api_client.get_products_chunked_datas(
            filters=filters,
            features=features,
            page_size=page_size,
            get_raw_response=True,
            get_all_pages=False,
        )
        logger.info(f'Total products to sync on: {chunked_data["count"]}')
        for i, products_chunk in chunked_data['chunks']:
            self.handle_products(products_chunk)
            logger.info(f'Processed {i} of {chunked_data["total_pages"]} pages')
        logger.info(f'Finished sync for vendor: {self.vendor}')

    def handle_products(self, products, do_handle_changed_products=True):
        if not self.vendor:
            raise Exception('No vendor for products import')

        products_ids = []
        if not products:
            return products_ids

        prefetch_related = []
        select_related = []

        qs = self.vendor.get_products_query(
            active_only=self.vendor.prop_sync_active_only,
            prefetch_related=prefetch_related, select_related=select_related)

        client_ids = [p['id'] for p in products]
        if client_ids:
            qs = qs.filter(id__in=client_ids)

        existing_by_client_id = self._get_existing_by_client_id(Product, qs=qs)

        self.update_stats('products', 'found_existing', len(existing_by_client_id))
        self.update_stats('products', 'total_parsed', len(products))

        self.changed_products_ids = defaultdict(list)

        dt_default = get_utc_now()
        with transaction.atomic():
            for data in products:
                self.service_api_client.fill_dt_created_updated(data, dt_default=dt_default)

                existing = existing_by_client_id.get(data['id'])
                if existing:
                    product = self._update_product(existing, data)
                else:
                    product = self._create_product(data)

                if product:
                    # product parsed
                    products_ids.append(product.id)

        if do_handle_changed_products:
            capture_message('_handle_changed_products is doing for vendor', tags={'vendor': self.vendor})
        else:
            logger.info(
                '_handle_changed_products is skipped due to do_handle_changed_products: False')
        return products_ids

    def _create_product(self, data):
        """
        {'brand_id': 1341,
               'brand_name': 'A PASSION PLAY',
               'description': '',
               'dt_created': '2021-11-10 12:26:50.941037',
               'dt_updated': '2024-05-24 04:57:16.455453',
               'gender': 'Жен.',
               'gender_code': 2,
               'gender_id': 1,
               'group': 'Верхняя одежда (Куртки)',
               'group_code': 'layer-3_jacket',
               'group_id': 86,
               'id': 709846,
               'is_deleted': False,
               'material': '',
               'name': 'A PASSION PLAY Джинсовая куртка',
               'picture_id': 4406058,
               'picture_url': 'https://media.garderobo.ru/5karmanov/big/e37cf02221c2622cd6ca9c255a36e7cc.jpg',
               'pictures': [{'id': 4406058,
                             'is_primary': True,
                             'url': 'https://media.garderobo.ru/5karmanov/big/e37cf02221c2622cd6ca9c255a36e7cc.jpg'}],
               'rc': 'Верхняя одежда > Джинсовые куртки',
               'rc_id': 65,
               'season': None,
               'season_id': None,
               'vendor_category_id': '268',
               'vendor_category_name': 'Женская коллекция > Верхняя одежда > '
                                       'Джинсовые куртки',
               'vendor_color': 'Синий',
               'vendor_id': 38},
        """
        dt_created = data.pop('dt_created', None) or get_utc_now()
        dt_updated = data.pop('dt_updated', None) or dt_created
        product = Product(
            id=data['id'],
            is_deleted=data.get('is_deleted', False),
            name=data['name'],
            vendor=self.vendor,
            dt_created=dt_created,
            dt_updated=dt_updated,
            data=data
        )
        product.save()
        self.update_stats('products', 'created')
        return product

    def _update_product(self, existing, data):
        # existing product was deleted, but now is updating to be .is_deleted=False
        # is_enabled_again_product = existing.is_deleted and not data['is_deleted']

        # cause .data is big json dict, we should not compare it with dict hashes, so, compare_dicts=False
        update_fields = self._update_fields(existing, data, [
            'is_deleted', 'name', 'data',
            'dt_created', 'dt_updated'
        ], compare_dicts=False, commit=False)

        if update_fields:
            for k in ['dt_created', 'dt_updated']:
                data.pop(k, None)
            existing.save(update_fields=update_fields)
            self.update_stats('products', 'updated')
        else:
            self.update_stats('products', 'not_changed')
        return existing

    def check_required_fields_are_set(self, data, fields,
                                      raise_on_error=False, log_on_error=False):
        not_set = [f for f in fields if f not in data or not data[f]]
        if not_set:
            message = 'Not all required fields {} are set in data: {}'.format(not_set, data)
            if raise_on_error:
                self.raise_exception(message)
            if log_on_error:
                self.logger.error(message)
            return False
        return True

    def import_vendors(self):
        vendors_data = self.service_api_client.get_vendors()
        return self.handle_vendors(vendors_data)

    def handle_vendors(self, vendors):
        if not vendors:
            return
        existing_vendors_by_client_id = self._get_existing_by_client_id(Vendor)

        self.update_stats('vendors', 'found_existing', len(existing_vendors_by_client_id))
        self.update_stats('vendors', 'total_parsed', len(vendors))

        for data in vendors:
            existing = existing_vendors_by_client_id.get(data['id'])
            if existing:
                self._update_vendor(existing, data)
            else:
                self._create_vendor(data)
        self.on_handle_vendors()

    def on_handle_vendors(self):
        pass

    def _create_vendor(self, data):
        #vendor = Vendor(id=data['id'], name=data['name'], code=data['code'], settings=data['settings'])
        vendor = Vendor(id=data['id'], code=data['code'], settings=data['settings'])
        vendor.save()
        self.update_stats('vendors', 'created')

    def _update_vendor(self, existing, data):
        update_fields = self._update_fields(existing, data, ['name', 'code', 'settings'])

        if update_fields:
            self.update_stats('vendors', 'updated')
        else:
            self.update_stats('vendors', 'not_changed')
        return existing, bool(update_fields)

    def init_stats(self):
        self.stats = {}
        for k in [
            # 'categories',
            'vendors', 'products',
        ]:
            self.stats[k] = defaultdict(int)

    def update_stats(self, k, kk, addon=1):
        self.stats[k][kk] += addon
